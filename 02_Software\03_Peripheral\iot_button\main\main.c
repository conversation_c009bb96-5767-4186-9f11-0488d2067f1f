/*
 * SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_check.h"

#include "nvs_flash.h"
#include "bsp_board.h"

static char *TAG = "app_main";

#define LOG_MEM_INFO (1)

void btn_boot_handle_cb(void *handle, void *arg)
{
    // 定义pcWriteBuffer并调用vTaskList
    static char pcWriteBuffer[512] = {0};
    vTaskList(pcWriteBuffer);

    // 打印任务列表
    printf("Name            State   Priority  Stack   Num  CoreID\n");
    printf("%s\n", pcWriteBuffer);
}

void btn_user_handle_cb(void *handle, void *arg)
{
    ESP_LOGI(TAG, "test user");
}

void app_main(void)
{
    /* Initialize NVS. */
    esp_err_t err = nvs_flash_init();
    if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        err = nvs_flash_init();
    }
    ESP_ERROR_CHECK(err);

    bsp_spiffs_mount();
    bsp_board_init();

    bsp_btn_register_callback(BSP_BUTTON_BOOT, BUTTON_PRESS_DOWN, btn_boot_handle_cb, NULL);
    bsp_btn_register_callback(BSP_BUTTON_USER, BUTTON_PRESS_DOWN, btn_user_handle_cb, NULL);

#if LOG_MEM_INFO
    static char buffer[128]; /* Make sure buffer is enough for `sprintf` */
    while (1)
    {
        /**
         * It's not recommended to frequently use functions like `heap_caps_get_free_size()` to obtain memory information
         * in practical applications, especially when the application extensively uses `malloc()` to dynamically allocate
         * a significant number of memory blocks. The frequent interrupt disabling may potentially lead to issues with other functionalities.
         */
        sprintf(buffer, "   Biggest /     Free /    Total\n"
                        "\t  SRAM : [%8d / %8d / %8d]\n"
                        "\t PSRAM : [%8d / %8d / %8d]",
                heap_caps_get_largest_free_block(MALLOC_CAP_INTERNAL),
                heap_caps_get_free_size(MALLOC_CAP_INTERNAL),
                heap_caps_get_total_size(MALLOC_CAP_INTERNAL),
                heap_caps_get_largest_free_block(MALLOC_CAP_SPIRAM),
                heap_caps_get_free_size(MALLOC_CAP_SPIRAM),
                heap_caps_get_total_size(MALLOC_CAP_SPIRAM));
        ESP_LOGI("MEM", "%s", buffer);

        vTaskDelay(pdMS_TO_TICKS(3000));
    }
#endif
}