/**
 * @FilePath: dht11_types.h
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-13 12:29:27
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-10-13 14:03:39
 * @Copyright: 2024 0668STO CO.,LTD. All Rights Reserved.
*/
#pragma once

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief DHT11 handle
 */
typedef struct dht11_t *dht11_handle_t;

/**
 * @brief DHT11 Configuration
 */
typedef struct {
    int dht11_gpio_num;      /*!< GPIO number that used by DHT11 */

    struct {
        uint32_t invert_in: 1;  /*!< Invert input signal */
    } flags;                    /*!< Extra driver flags */
} dht11_config_t;

#ifdef __cplusplus
}
#endif
