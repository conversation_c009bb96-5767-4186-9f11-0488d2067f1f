/**
 * @FilePath: dht11_rmt_dev.c
 * @Author: Ah<PERSON><PERSON><PERSON>
 * @Date: 2024-10-13 12:25:17
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-10-13 20:03:53
 * @Copyright: 2024 0668STO CO.,LTD. All Rights Reserved.
*/
#include <stdlib.h>
#include <string.h>
#include <sys/cdefs.h>
#include "esp_log.h"
#include "esp_check.h"
#include "driver/rmt_rx.h"
#include "driver/gpio.h"
#include <rom/ets_sys.h>
#include "dht11.h"
#include "dht11_interface.h"

#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

#define DHT11_RMT_DEFAULT_RESOLUTION 1000000 // 1MHz resolution
#define DHT11_RMT_DEFAULT_MEM_BLOCK_SYMBOLS 64

static const char *TAG = "dht11_rmt";

typedef struct
{
    dht11_t base;
    rmt_channel_handle_t rmt_chan;
    QueueHandle_t rx_receive_queue;
    int gpio_num;
} dht11_rmt_obj;

static esp_err_t dht11_rmt_init(dht11_t *sensor, int gpio_num)
{
    dht11_rmt_obj *rmt_sensor = __containerof(sensor, dht11_rmt_obj, base);
    rmt_sensor->gpio_num = gpio_num;
    gpio_set_direction(gpio_num, GPIO_MODE_INPUT_OUTPUT);
    return ESP_OK;
}

static esp_err_t parse_items(rmt_symbol_word_t *item, int item_num, int *humidity, int *temp_x10)
{
    int i = 0;
    unsigned int rh = 0, temp = 0, checksum = 0;
    if (item_num < 42)
    { // 检查是否有足够的脉冲数
        ESP_LOGI(TAG, "item_num < 42  %d", item_num);
        return ESP_FAIL;
    }
    item++; // 跳过开始信号脉冲

    for (i = 0; i < 16; i++, item++) // 提取湿度数据
    {
        uint16_t duration = 0;
        if (item->level0)
            duration = item->duration0;
        else
            duration = item->duration1;
        rh = (rh << 1) + (duration < 35 ? 0 : 1);
    }

    for (i = 0; i < 16; i++, item++) // 提取温度数据
    {
        uint16_t duration = 0;
        if (item->level0)
            duration = item->duration0;
        else
            duration = item->duration1;
        temp = (temp << 1) + (duration < 35 ? 0 : 1);
    }

    for (i = 0; i < 8; i++, item++)
    { // 提取校验数据

        uint16_t duration = 0;
        if (item->level0)
            duration = item->duration0;
        else
            duration = item->duration1;
        checksum = (checksum << 1) + (duration < 35 ? 0 : 1);
    }
    // 检查校验
    if ((((temp >> 8) + temp + (rh >> 8) + rh) & 0xFF) != checksum)
    {
        ESP_LOGI(TAG, "Checksum failure %4X %4X %2X\n", temp, rh, checksum);
        return ESP_FAIL;
    }
    // 返回数据
    *humidity = rh >> 8;
    *temp_x10 = (temp >> 8) * 10 + (temp & 0xFF);
    return ESP_OK;
}

static esp_err_t dht11_rmt_read(dht11_t *sensor, int *temperature, int *humidity)
{
    dht11_rmt_obj *rmt_sensor = __containerof(sensor, dht11_rmt_obj, base);
    // 发送20ms开始信号脉冲启动DHT11单总线
    gpio_set_direction(rmt_sensor->gpio_num, GPIO_MODE_OUTPUT);
    gpio_set_level(rmt_sensor->gpio_num, 1);
    ets_delay_us(1000);
    gpio_set_level(rmt_sensor->gpio_num, 0);
    ets_delay_us(20000);
    // 拉高20us
    gpio_set_level(rmt_sensor->gpio_num, 1);
    ets_delay_us(20);
    // 信号线设置为输入准备接收数据
    gpio_set_direction(rmt_sensor->gpio_num, GPIO_MODE_INPUT);
    gpio_set_pull_mode(rmt_sensor->gpio_num, GPIO_PULLUP_ONLY);

    // 启动RMT接收器以获取数据
    rmt_receive_config_t receive_config = {
        .signal_range_min_ns = 1000,       // 最小脉冲宽度(1us),信号长度小于这个值，视为干扰
        .signal_range_max_ns = 200 * 1000, // 最大脉冲宽度(200us)，信号长度大于这个值，视为结束信号
    };

    static rmt_symbol_word_t raw_symbols[64]; // 接收缓存
    static rmt_rx_done_event_data_t rx_data;  // 实际接收到的数据
    ESP_ERROR_CHECK(rmt_receive(rmt_sensor->rmt_chan, raw_symbols, sizeof(raw_symbols), &receive_config));
    
    // wait for RX done signal
    if (xQueueReceive(rmt_sensor->rx_receive_queue, &rx_data, pdMS_TO_TICKS(1000)) == pdTRUE)
    {
        // parse the receive symbols and print the result
        return parse_items(rx_data.received_symbols, rx_data.num_symbols, humidity, temperature);
    }
    return ESP_OK;
}

static esp_err_t dht11_rmt_deinit(dht11_t *sensor)
{
    dht11_rmt_obj *rmt_sensor = __containerof(sensor, dht11_rmt_obj, base);
    ESP_RETURN_ON_ERROR(rmt_del_channel(rmt_sensor->rmt_chan), TAG, "delete RMT channel failed");
    free(rmt_sensor);
    return ESP_OK;
}

//接收完成回调函数
static bool dht11_rmt_rx_done_callback(rmt_channel_handle_t channel, const rmt_rx_done_event_data_t *edata, void *user_data)
{
    BaseType_t high_task_wakeup = pdFALSE;
    QueueHandle_t rx_receive_queue = (QueueHandle_t)user_data;
    // send the received RMT symbols to the parser task
    xQueueSendFromISR(rx_receive_queue, edata, &high_task_wakeup);
    return high_task_wakeup == pdTRUE;
}


esp_err_t dht11_new_rmt_device(const dht11_config_t *dht11_config, const dht11_rmt_config_t *rmt_config, dht11_handle_t *ret_sensor)
{
    dht11_rmt_obj *rmt_sensor = NULL;
    esp_err_t ret = ESP_OK;
    ESP_GOTO_ON_FALSE(dht11_config && rmt_config && ret_sensor, ESP_ERR_INVALID_ARG, err, TAG, "invalid argument");

    rmt_sensor = calloc(1, sizeof(dht11_rmt_obj));
    ESP_GOTO_ON_FALSE(rmt_sensor, ESP_ERR_NO_MEM, err, TAG, "no mem for rmt sensor");

    uint32_t resolution = rmt_config->resolution_hz ? rmt_config->resolution_hz : DHT11_RMT_DEFAULT_RESOLUTION;
    size_t mem_block_symbols = rmt_config->mem_block_symbols ? rmt_config->mem_block_symbols : DHT11_RMT_DEFAULT_MEM_BLOCK_SYMBOLS;

    rmt_rx_channel_config_t rmt_chan_config = {
        .clk_src = rmt_config->clk_src,
        .gpio_num = dht11_config->dht11_gpio_num,
        .mem_block_symbols = mem_block_symbols,
        .resolution_hz = resolution,
        .flags.invert_in = dht11_config->flags.invert_in,
        .flags.with_dma = rmt_config->flags.with_dma,
    };
    ESP_GOTO_ON_ERROR(rmt_new_rx_channel(&rmt_chan_config, &rmt_sensor->rmt_chan), err, TAG, "create RMT RX channel failed");

    // 新建接收数据队列
    rmt_sensor->rx_receive_queue = xQueueCreate(20, sizeof(rmt_rx_done_event_data_t));
    assert(rmt_sensor->rx_receive_queue);

    // 注册接收完成回调函数
    ESP_LOGI(TAG, "register RX done callback");
    rmt_rx_event_callbacks_t cbs = {
        .on_recv_done = dht11_rmt_rx_done_callback,
    };
    ESP_ERROR_CHECK(rmt_rx_register_event_callbacks(rmt_sensor->rmt_chan, &cbs, rmt_sensor->rx_receive_queue));

    rmt_sensor->base.init = dht11_rmt_init;
    rmt_sensor->base.read = dht11_rmt_read;
    rmt_sensor->base.deinit = dht11_rmt_deinit;

    ESP_GOTO_ON_ERROR(rmt_sensor->base.init(&rmt_sensor->base, dht11_config->dht11_gpio_num), err, TAG, "init dht11 failed");

    // 启用 RMT 通道
    ESP_GOTO_ON_ERROR(rmt_enable(rmt_sensor->rmt_chan), err, TAG, "enable RMT channel failed");

    *ret_sensor = &rmt_sensor->base;
    return ESP_OK;
err:
    if (rmt_sensor)
    {
        if (rmt_sensor->rmt_chan)
        {
            rmt_del_channel(rmt_sensor->rmt_chan);
        }
        free(rmt_sensor);
    }
    return ret;
}