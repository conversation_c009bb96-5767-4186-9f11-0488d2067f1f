library,object,function,option
libble_app.a,ble_hw.c.o,r_ble_hw_resolv_list_get_cur_entry,
libble_app.a,ble_ll_adv.c.o,r_ble_ll_adv_set_sched,
libble_app.a,ble_ll_adv.c.o,r_ble_ll_adv_sync_pdu_make,
libble_app.a,ble_ll_adv.c.o,r_ble_ll_adv_sync_calculate,
libble_app.a,ble_ll_conn.c.o,r_ble_ll_conn_is_dev_connected,
libble_app.a,ble_ll_ctrl.c.o,r_ble_ll_ctrl_tx_done,
libble_app.a,ble_lll_adv.c.o,r_ble_lll_adv_aux_scannable_pdu_payload_len,
libble_app.a,ble_lll_adv.c.o,r_ble_lll_adv_halt,
libble_app.a,ble_lll_adv.c.o,r_ble_lll_adv_periodic_schedule_next,
libble_app.a,ble_lll_conn.c.o,r_ble_lll_conn_cth_flow_free_credit,
libble_app.a,ble_lll_conn.c.o,r_ble_lll_conn_update_encryption,
libble_app.a,ble_lll_conn.c.o,r_ble_lll_conn_set_slave_flow_control,
libble_app.a,ble_lll_conn.c.o,r_ble_lll_init_rx_pkt_isr,
libble_app.a,ble_lll_conn.c.o,r_ble_lll_conn_get_rx_mbuf,
libble_app.a,ble_lll_rfmgmt.c.o,r_ble_lll_rfmgmt_enable,
libble_app.a,ble_lll_rfmgmt.c.o,r_ble_lll_rfmgmt_timer_reschedule,
libble_app.a,ble_lll_rfmgmt.c.o,r_ble_lll_rfmgmt_timer_exp,
libble_app.a,ble_lll_scan.c.o,r_ble_lll_scan_targeta_is_matched,
libble_app.a,ble_lll_scan.c.o,r_ble_lll_scan_rx_isr_on_legacy,
libble_app.a,ble_lll_scan.c.o,r_ble_lll_scan_rx_isr_on_aux,
libble_app.a,ble_lll_scan.c.o,r_ble_lll_scan_process_rsp_in_isr,
libble_app.a,ble_lll_scan.c.o,r_ble_lll_scan_rx_pkt_isr,
libble_app.a,ble_lll_sched.c.o,r_ble_lll_sched_execute_check,
libble_app.a,ble_lll_sync.c.o,r_ble_lll_sync_event_start_cb,
libble_app.a,ble_phy.c.o,r_ble_phy_set_rxhdr,
libble_app.a,ble_phy.c.o,r_ble_phy_update_conn_sequence,
libble_app.a,ble_phy.c.o,r_ble_phy_set_sequence_mode,
libble_app.a,ble_phy.c.o,r_ble_phy_txpower_round,
libble_app.a,os_mempool.c.obj,r_os_memblock_put,
libbootloader_support.a,bootloader_flash.c.obj,bootloader_read_flash_id,
libbootloader_support.a,flash_encrypt.c.obj,esp_flash_encryption_enabled,
libbt.a,bt_osi_mem.c.obj,bt_osi_mem_calloc,CONFIG_BT_ENABLED
libbt.a,bt_osi_mem.c.obj,bt_osi_mem_malloc,CONFIG_BT_ENABLED
libbt.a,bt_osi_mem.c.obj,bt_osi_mem_malloc_internal,CONFIG_BT_ENABLED
libbt.a,bt_osi_mem.c.obj,bt_osi_mem_free,CONFIG_BT_ENABLED
libbt.a,bt.c.obj,esp_reset_rpa_moudle,CONFIG_BT_ENABLED
libbt.a,bt.c.obj,osi_assert_wrapper,CONFIG_BT_ENABLED
libbt.a,bt.c.obj,osi_random_wrapper,CONFIG_BT_ENABLED
libbt.a,nimble_port.c.obj,nimble_port_run,CONFIG_BT_NIMBLE_ENABLED
libbt.a,nimble_port.c.obj,nimble_port_get_dflt_eventq,CONFIG_BT_NIMBLE_ENABLED
libbt.a,npl_os_freertos.c.obj,os_callout_timer_cb,CONFIG_BT_ENABLED && !CONFIG_BT_NIMBLE_USE_ESP_TIMER
libbt.a,npl_os_freertos.c.obj,npl_freertos_event_run,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_stop,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_time_get,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_reset,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_is_active,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_get_ticks,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_remaining_ticks,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_time_delay,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_eventq_is_empty,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_mutex_pend,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_mutex_release,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_sem_init,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_sem_pend,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_sem_release,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_init,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_deinit,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_event_is_queued,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_event_get_arg,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_time_ms_to_ticks32,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_time_ticks_to_ms32,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_hw_is_in_critical,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_get_time_forever,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_os_started,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_get_current_task_id,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_event_reset,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_mem_reset,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_event_init,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_sem_get_count,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_eventq_remove,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_hw_exit_critical,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_mutex_init,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_hw_enter_critical,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_eventq_put,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_eventq_get,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_sem_deinit,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_mutex_deinit,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_eventq_deinit,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_eventq_init,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_event_deinit,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_time_ticks_to_ms,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_time_ms_to_ticks,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_callout_set_arg,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_event_set_arg,CONFIG_BT_ENABLED
libbt.a,npl_os_freertos.c.obj,npl_freertos_eventq_put,CONFIG_BT_ENABLED
libdriver.a,gpio.c.obj,gpio_intr_service,
libesp_app_format.a,esp_app_desc.c.obj,esp_app_get_elf_sha256,
libesp_hw_support.a,cpu.c.obj,esp_cpu_wait_for_intr,
libesp_hw_support.a,cpu.c.obj,esp_cpu_reset,
libesp_hw_support.a,esp_clk.c.obj,esp_clk_cpu_freq,
libesp_hw_support.a,esp_clk.c.obj,esp_clk_apb_freq,
libesp_hw_support.a,esp_clk.c.obj,esp_clk_xtal_freq,
libesp_hw_support.a,esp_memory_utils.c.obj,esp_ptr_byte_accessible,
libesp_hw_support.a,hw_random.c.obj,esp_random,
libesp_hw_support.a,intr_alloc.c.obj,shared_intr_isr,
libesp_hw_support.a,intr_alloc.c.obj,esp_intr_noniram_disable,
libesp_hw_support.a,intr_alloc.c.obj,esp_intr_noniram_enable,
libesp_hw_support.a,intr_alloc.c.obj,esp_intr_enable,
libesp_hw_support.a,intr_alloc.c.obj,esp_intr_disable,
libesp_hw_support.a,periph_ctrl.c.obj,wifi_bt_common_module_enable,
libesp_hw_support.a,periph_ctrl.c.obj,wifi_bt_common_module_disable,
libesp_hw_support.a,regi2c_ctrl.c.obj,regi2c_ctrl_read_reg,
libesp_hw_support.a,regi2c_ctrl.c.obj,regi2c_ctrl_read_reg_mask,
libesp_hw_support.a,regi2c_ctrl.c.obj,regi2c_ctrl_write_reg,
libesp_hw_support.a,regi2c_ctrl.c.obj,regi2c_ctrl_write_reg_mask,
libesp_hw_support.a,regi2c_ctrl.c.obj,regi2c_enter_critical,
libesp_hw_support.a,regi2c_ctrl.c.obj,regi2c_exit_critical,
libesp_hw_support.a,regi2c_ctrl.c.obj,regi2c_analog_cali_reg_read,
libesp_hw_support.a,regi2c_ctrl.c.obj,regi2c_analog_cali_reg_write,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_32k_enable_external,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_fast_src_set,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_slow_freq_get_hz,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_slow_src_get,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_slow_src_set,
libesp_hw_support.a,rtc_clk.c.obj,rtc_dig_clk8m_disable,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_set_config_fast,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_8m_enable,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_8md256_enabled,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_bbpll_configure,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_bbpll_enable,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_get_config,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_mhz_to_config,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_set_config,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_to_8m,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_to_pll_mhz,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_set_xtal,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_xtal_freq_get,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_to_xtal,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_bbpll_disable,
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_apb_freq_update,
libesp_hw_support.a,rtc_clk.c.obj,clk_ll_rtc_slow_get_src,FALSE
libesp_hw_support.a,rtc_init.c.obj,rtc_vddsdio_set_config,
libesp_hw_support.a,rtc_module.c.obj,rtc_isr,
libesp_hw_support.a,rtc_module.c.obj,rtc_isr_noniram_disable,
libesp_hw_support.a,rtc_module.c.obj,rtc_isr_noniram_enable,
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_pu,
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_finish,
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_get_default_config,
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_init,
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_low_init,
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_start,
libesp_hw_support.a,rtc_time.c.obj,rtc_clk_cal,
libesp_hw_support.a,rtc_time.c.obj,rtc_clk_cal_internal,
libesp_hw_support.a,rtc_time.c.obj,rtc_time_get,
libesp_hw_support.a,rtc_time.c.obj,rtc_time_us_to_slowclk,
libesp_hw_support.a,rtc_time.c.obj,rtc_time_slowclk_to_us,
libesp_hw_support.a,sleep_modes.c.obj,periph_ll_periph_enabled,
libesp_hw_support.a,sleep_modes.c.obj,flush_uarts,
libesp_hw_support.a,sleep_modes.c.obj,suspend_uarts,
libesp_hw_support.a,sleep_modes.c.obj,resume_uarts,
libesp_hw_support.a,sleep_modes.c.obj,esp_sleep_start,
libesp_hw_support.a,sleep_modes.c.obj,esp_deep_sleep_start,
libesp_hw_support.a,sleep_modes.c.obj,esp_light_sleep_inner,
libesp_phy.a,phy_init.c.obj,esp_phy_common_clock_enable,
libesp_phy.a,phy_init.c.obj,esp_phy_common_clock_disable,
libesp_phy.a,phy_init.c.obj,esp_wifi_bt_power_domain_on,
libesp_phy.a,phy_override.c.obj,phy_i2c_enter_critical,
libesp_phy.a,phy_override.c.obj,phy_i2c_exit_critical,
libesp_pm.a,pm_locks.c.obj,esp_pm_lock_acquire,
libesp_pm.a,pm_locks.c.obj,esp_pm_lock_release,
libesp_pm.a,pm_impl.c.obj,get_lowest_allowed_mode,
libesp_pm.a,pm_impl.c.obj,esp_pm_impl_switch_mode,
libesp_pm.a,pm_impl.c.obj,on_freq_update,
libesp_pm.a,pm_impl.c.obj,vApplicationSleep,CONFIG_FREERTOS_USE_TICKLESS_IDLE
libesp_pm.a,pm_impl.c.obj,do_switch,
libesp_ringbuf.a,ringbuf.c.obj,prvCheckItemAvail,
libesp_ringbuf.a,ringbuf.c.obj,prvGetFreeSize,
libesp_ringbuf.a,ringbuf.c.obj,prvReceiveGenericFromISR,
libesp_ringbuf.a,ringbuf.c.obj,xRingbufferGetMaxItemSize,
libesp_rom.a,esp_rom_systimer.c.obj,systimer_hal_init,
libesp_rom.a,esp_rom_systimer.c.obj,systimer_hal_set_alarm_period,
libesp_rom.a,esp_rom_systimer.c.obj,systimer_hal_set_alarm_target,
libesp_rom.a,esp_rom_systimer.c.obj,systimer_hal_set_tick_rate_ops,
libesp_rom.a,esp_rom_uart.c.obj,esp_rom_uart_set_clock_baudrate,
libesp_system.a,brownout.c.obj,rtc_brownout_isr_handler,
libesp_system.a,cache_err_int.c.obj,esp_cache_err_get_cpuid,
libesp_system.a,cpu_start.c.obj,call_start_cpu0,
libesp_system.a,crosscore_int.c.obj,esp_crosscore_int_send,
libesp_system.a,crosscore_int.c.obj,esp_crosscore_int_send_yield,
libesp_system.a,esp_system.c.obj,esp_restart,
libesp_system.a,esp_system.c.obj,esp_system_abort,
libesp_system.a,reset_reason.c.obj,esp_reset_reason_set_hint,
libesp_system.a,reset_reason.c.obj,esp_reset_reason_get_hint,
libesp_system.a,ubsan.c.obj,__ubsan_include,
libesp_timer.a,esp_timer.c.obj,esp_timer_get_next_alarm_for_wake_up,
libesp_timer.a,esp_timer.c.obj,timer_list_unlock,!CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD
libesp_timer.a,esp_timer.c.obj,timer_list_lock,!CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD
libesp_timer.a,esp_timer.c.obj,timer_armed,
libesp_timer.a,esp_timer.c.obj,timer_remove,
libesp_timer.a,esp_timer.c.obj,timer_insert,!CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD
libesp_timer.a,esp_timer.c.obj,esp_timer_start_once,
libesp_timer.a,esp_timer.c.obj,esp_timer_start_periodic,
libesp_timer.a,esp_timer.c.obj,esp_timer_stop,
libesp_timer.a,esp_timer.c.obj,esp_timer_get_expiry_time,
libesp_timer.a,esp_timer_impl_systimer.c.obj,esp_timer_impl_get_time,!CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD
libesp_timer.a,esp_timer_impl_systimer.c.obj,esp_timer_impl_set_alarm_id,!CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD
libesp_timer.a,esp_timer_impl_systimer.c.obj,esp_timer_impl_update_apb_freq,
libesp_timer.a,esp_timer_impl_systimer.c.obj,esp_timer_impl_get_min_period_us,
libesp_timer.a,ets_timer_legacy.c.obj,timer_initialized,
libesp_timer.a,ets_timer_legacy.c.obj,ets_timer_arm_us,
libesp_timer.a,ets_timer_legacy.c.obj,ets_timer_arm,
libesp_timer.a,ets_timer_legacy.c.obj,ets_timer_disarm,
libesp_timer.a,system_time.c.obj,esp_system_get_time,
libesp_wifi.a,esp_adapter.c.obj,semphr_take_from_isr_wrapper,
libesp_wifi.a,esp_adapter.c.obj,wifi_realloc,
libesp_wifi.a,esp_adapter.c.obj,coex_event_duration_get_wrapper,
libesp_wifi.a,esp_adapter.c.obj,coex_schm_interval_set_wrapper,
libesp_wifi.a,esp_adapter.c.obj,esp_empty_wrapper,
libesp_wifi.a,esp_adapter.c.obj,wifi_calloc,
libesp_wifi.a,esp_adapter.c.obj,wifi_zalloc_wrapper,
libesp_wifi.a,esp_adapter.c.obj,env_is_chip_wrapper,
libesp_wifi.a,esp_adapter.c.obj,is_from_isr_wrapper,
libesp_wifi.a,esp_adapter.c.obj,semphr_give_from_isr_wrapper,
libesp_wifi.a,esp_adapter.c.obj,mutex_lock_wrapper,
libesp_wifi.a,esp_adapter.c.obj,mutex_unlock_wrapper,
libesp_wifi.a,esp_adapter.c.obj,task_ms_to_tick_wrapper,
libesp_wifi.a,esp_adapter.c.obj,wifi_apb80m_request_wrapper,
libesp_wifi.a,esp_adapter.c.obj,wifi_apb80m_release_wrapper,
libesp_wifi.a,esp_adapter.c.obj,timer_arm_wrapper,
libesp_wifi.a,esp_adapter.c.obj,wifi_malloc,
libesp_wifi.a,esp_adapter.c.obj,timer_disarm_wrapper,
libesp_wifi.a,esp_adapter.c.obj,timer_arm_us_wrapper,
libesp_wifi.a,esp_adapter.c.obj,wifi_rtc_enable_iso_wrapper,
libesp_wifi.a,esp_adapter.c.obj,wifi_rtc_disable_iso_wrapper,
libesp_wifi.a,esp_adapter.c.obj,malloc_internal_wrapper,
libesp_wifi.a,esp_adapter.c.obj,realloc_internal_wrapper,
libesp_wifi.a,esp_adapter.c.obj,calloc_internal_wrapper,
libesp_wifi.a,esp_adapter.c.obj,zalloc_internal_wrapper,
libesp_wifi.a,esp_adapter.c.obj,coex_status_get_wrapper,
libesp_wifi.a,esp_adapter.c.obj,coex_wifi_release_wrapper,
libfreertos.a,list.c.obj,uxListRemove,FALSE
libfreertos.a,list.c.obj,vListInitialise,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,list.c.obj,vListInitialiseItem,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,list.c.obj,vListInsert,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,list.c.obj,vListInsertEnd,FALSE
libfreertos.a,port.c.obj,vApplicationStackOverflowHook,FALSE
libfreertos.a,port.c.obj,vPortYieldOtherCore,FALSE
libfreertos.a,port.c.obj,vPortYield,
libfreertos.a,port_common.c.obj,xPortcheckValidStackMem,
libfreertos.a,port_common.c.obj,vApplicationGetTimerTaskMemory,
libfreertos.a,port_common.c.obj,esp_startup_start_app_common,
libfreertos.a,port_common.c.obj,xPortCheckValidTCBMem,
libfreertos.a,port_common.c.obj,vApplicationGetIdleTaskMemory,
libfreertos.a,port_systick.c.obj,vPortSetupTimer,
libfreertos.a,port_systick.c.obj,xPortSysTickHandler,FALSE
libfreertos.a,queue.c.obj,prvCopyDataFromQueue,
libfreertos.a,queue.c.obj,prvGetDisinheritPriorityAfterTimeout,
libfreertos.a,queue.c.obj,prvIsQueueEmpty,
libfreertos.a,queue.c.obj,prvNotifyQueueSetContainer,
libfreertos.a,queue.c.obj,xQueueReceive,
libfreertos.a,queue.c.obj,prvUnlockQueue,
libfreertos.a,queue.c.obj,xQueueSemaphoreTake,
libfreertos.a,queue.c.obj,xQueueReceiveFromISR,
libfreertos.a,queue.c.obj,uxQueueMessagesWaitingFromISR,
libfreertos.a,queue.c.obj,xQueueIsQueueEmptyFromISR,
libfreertos.a,queue.c.obj,xQueueGiveFromISR,
libfreertos.a,tasks.c.obj,__getreent,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,pcTaskGetName,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,prvAddCurrentTaskToDelayedList,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,prvDeleteTLS,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,pvTaskIncrementMutexHeldCount,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,taskSelectHighestPriorityTaskSMP,FALSE
libfreertos.a,tasks.c.obj,taskYIELD_OTHER_CORE,FALSE
libfreertos.a,tasks.c.obj,vTaskGetSnapshot,CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH
libfreertos.a,tasks.c.obj,vTaskInternalSetTimeOutState,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,vTaskPlaceOnEventList,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,vTaskPlaceOnEventListRestricted,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,vTaskPlaceOnUnorderedEventList,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,vTaskPriorityDisinheritAfterTimeout,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,vTaskReleaseEventListLock,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,vTaskTakeEventListLock,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,xTaskCheckForTimeOut,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,xTaskGetCurrentTaskHandle,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,xTaskGetSchedulerState,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,xTaskGetTickCount,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,xTaskPriorityDisinherit,FALSE
libfreertos.a,tasks.c.obj,xTaskPriorityInherit,CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH
libfreertos.a,tasks.c.obj,prvGetExpectedIdleTime,FALSE
libfreertos.a,tasks.c.obj,vTaskStepTick,FALSE
libhal.a,brownout_hal.c.obj,brownout_hal_intr_clear,
libhal.a,efuse_hal.c.obj,efuse_hal_chip_revision,
libhal.a,efuse_hal.c.obj,efuse_hal_get_major_chip_version,
libhal.a,efuse_hal.c.obj,efuse_hal_get_minor_chip_version,
libheap.a,heap_caps.c.obj,dram_alloc_to_iram_addr,
libheap.a,heap_caps.c.obj,heap_caps_free,
libheap.a,heap_caps.c.obj,heap_caps_realloc_base,
libheap.a,heap_caps.c.obj,heap_caps_realloc,
libheap.a,heap_caps.c.obj,heap_caps_calloc_base,
libheap.a,heap_caps.c.obj,heap_caps_calloc,
libheap.a,heap_caps.c.obj,heap_caps_malloc_base,
libheap.a,heap_caps.c.obj,heap_caps_malloc,
libheap.a,heap_caps.c.obj,heap_caps_malloc_default,
libheap.a,heap_caps.c.obj,heap_caps_realloc_default,
libheap.a,heap_caps.c.obj,find_containing_heap,
libheap.a,multi_heap.c.obj,_multi_heap_lock,
libheap.a,multi_heap.c.obj,multi_heap_in_rom_init,
liblog.a,log_freertos.c.obj,esp_log_timestamp,
liblog.a,log_freertos.c.obj,esp_log_impl_lock,
liblog.a,log_freertos.c.obj,esp_log_impl_unlock,
liblog.a,log_freertos.c.obj,esp_log_impl_lock_timeout,
liblog.a,log_freertos.c.obj,esp_log_early_timestamp,
liblog.a,log.c.obj,esp_log_write,
libmbedcrypto.a,esp_mem.c.obj,esp_mbedtls_mem_calloc,!CONFIG_MBEDTLS_CUSTOM_MEM_ALLOC
libmbedcrypto.a,esp_mem.c.obj,esp_mbedtls_mem_free,!CONFIG_MBEDTLS_CUSTOM_MEM_ALLOC
libnewlib.a,assert.c.obj,__assert_func,
libnewlib.a,assert.c.obj,newlib_include_assert_impl,
libnewlib.a,heap.c.obj,_calloc_r,
libnewlib.a,heap.c.obj,_free_r,
libnewlib.a,heap.c.obj,_malloc_r,
libnewlib.a,heap.c.obj,_realloc_r,
libnewlib.a,heap.c.obj,calloc,
libnewlib.a,heap.c.obj,cfree,
libnewlib.a,heap.c.obj,free,
libnewlib.a,heap.c.obj,malloc,
libnewlib.a,heap.c.obj,newlib_include_heap_impl,
libnewlib.a,heap.c.obj,realloc,
libnewlib.a,locks.c.obj,_lock_try_acquire_recursive,
libnewlib.a,locks.c.obj,lock_release_generic,
libnewlib.a,locks.c.obj,_lock_release,
libnewlib.a,locks.c.obj,_lock_release_recursive,
libnewlib.a,locks.c.obj,__retarget_lock_init,
libnewlib.a,locks.c.obj,__retarget_lock_init_recursive,
libnewlib.a,locks.c.obj,__retarget_lock_close,
libnewlib.a,locks.c.obj,__retarget_lock_close_recursive,
libnewlib.a,locks.c.obj,check_lock_nonzero,
libnewlib.a,locks.c.obj,__retarget_lock_acquire,
libnewlib.a,locks.c.obj,lock_init_generic,
libnewlib.a,locks.c.obj,__retarget_lock_acquire_recursive,
libnewlib.a,locks.c.obj,__retarget_lock_try_acquire,
libnewlib.a,locks.c.obj,__retarget_lock_try_acquire_recursive,
libnewlib.a,locks.c.obj,__retarget_lock_release,
libnewlib.a,locks.c.obj,__retarget_lock_release_recursive,
libnewlib.a,locks.c.obj,_lock_close,
libnewlib.a,locks.c.obj,lock_acquire_generic,
libnewlib.a,locks.c.obj,_lock_acquire,
libnewlib.a,locks.c.obj,_lock_acquire_recursive,
libnewlib.a,locks.c.obj,_lock_try_acquire,
libnewlib.a,reent_init.c.obj,esp_reent_init,
libnewlib.a,time.c.obj,_times_r,
libnewlib.a,time.c.obj,_gettimeofday_r,
libpp.a,pp_debug.o,wifi_gpio_debug,
libpthread.a,pthread.c.obj,pthread_mutex_lock_internal,
libpthread.a,pthread.c.obj,pthread_mutex_lock,
libpthread.a,pthread.c.obj,pthread_mutex_unlock,
libriscv.a,interrupt.c.obj,intr_handler_get,
libriscv.a,interrupt.c.obj,intr_handler_set,
libriscv.a,interrupt.c.obj,intr_matrix_route,
libspi_flash.a,flash_brownout_hook.c.obj,spi_flash_needs_reset_check,FALSE
libspi_flash.a,flash_brownout_hook.c.obj,spi_flash_set_erasing_flag,FALSE
libspi_flash.a,flash_ops.c.obj,spi_flash_guard_set,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,flash_ops.c.obj,spi_flash_malloc_internal,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,flash_ops.c.obj,spi_flash_rom_impl_init,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,flash_ops.c.obj,esp_mspi_pin_init,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,flash_ops.c.obj,spi_flash_init_chip_state,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_app.c.obj,delay_us,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_app.c.obj,get_buffer_malloc,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_app.c.obj,release_buffer_malloc,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_app.c.obj,main_flash_region_protected,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_app.c.obj,main_flash_op_status,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_app.c.obj,spi1_flash_os_check_yield,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_app.c.obj,spi1_flash_os_yield,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_noos.c.obj,start,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_noos.c.obj,end,CONFIG_SPI_FLASH_ROM_IMPL
libspi_flash.a,spi_flash_os_func_noos.c.obj,delay_us,CONFIG_SPI_FLASH_ROM_IMPL
