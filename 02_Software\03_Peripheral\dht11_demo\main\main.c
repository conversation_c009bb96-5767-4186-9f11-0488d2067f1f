/*
 * SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <freertos/queue.h>
#include "esp_log.h"
#include "esp_check.h"

#include "nvs_flash.h"

#include "dht11.h"
#include "dht11_interface.h"

#include "sw_dht11.h"

static char *TAG = "app_main";

#define LOG_MEM_INFO (1)

static dht11_handle_t dht11;

#define BSP_DHT11_IO		(47)


// 温度 湿度变量
int temp = 0,hum = 0;

/**
 * @brief dht11 configuration structure
 *
 * This configuration is used by default in bsp_dht11_init()
 */
static const dht11_config_t bsp_dht11_config = {
    .dht11_gpio_num = BSP_DHT11_IO,
    .flags.invert_in = false,
};

static const dht11_rmt_config_t bsp_dht11_rmt_config = {
    .clk_src = RMT_CLK_SRC_DEFAULT,
    .resolution_hz = 1 * 1000 * 1000,
    .flags.with_dma = false,
};

esp_err_t bsp_dht11_init()
{
    ESP_LOGI(TAG, "DHT11_GPIO setting %d", bsp_dht11_config.dht11_gpio_num);

    ESP_ERROR_CHECK(dht11_new_rmt_device(&bsp_dht11_config, &bsp_dht11_rmt_config, &dht11));
    dht11_init(dht11, bsp_dht11_config.dht11_gpio_num);

    return ESP_OK;
}

esp_err_t bsp_dht11_read(int *temperature, int *humidity)
{
    esp_err_t ret = ESP_OK;

    ret = dht11_read(dht11, temperature, humidity);

    return ret;
}

void app_main(void)
{
	// ESP_ERROR_CHECK(nvs_flash_init());
	vTaskDelay(100 / portTICK_PERIOD_MS);
	ESP_ERROR_CHECK(bsp_dht11_init());
	while (1){
		if (bsp_dht11_read(&temp, &hum) == ESP_OK){
			ESP_LOGI(TAG, "temperature : %i.%i C     humidity : %i%%", temp / 10, temp % 10, hum);
		}

		// DHT11();
		// ESP_LOGI(TAG, "temperature : %i C     humidity : %i%%", Temperature, Humidity);
		vTaskDelay(500 / portTICK_PERIOD_MS);
	}
}
