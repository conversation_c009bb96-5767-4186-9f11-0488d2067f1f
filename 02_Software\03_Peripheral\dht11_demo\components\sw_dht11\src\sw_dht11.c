/**
 * @FilePath: sw_dht11.c
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-24 12:20:56
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-11-24 13:30:30
 * @Copyright: 2024 0668STO CO.,LTD. All Rights Reserved.
*/
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "rom/ets_sys.h"
#include "rom/gpio.h"
#include "driver/gpio.h"
#include "sw_dht11.h"

// 宏定义和全局变量
#define DHT11_PIN_NUM       (47)

uint8_t ucharFLAG,uchartemp;
uint8_t Humidity,Temperature;
uint8_t ucharT_data_H,ucharT_data_L,ucharRH_data_H,ucharRH_data_L,ucharcheckdata;
uint8_t ucharT_data_H_temp,ucharT_data_L_temp,ucharRH_data_H_temp,ucharRH_data_L_temp,ucharcheckdata_temp;
uint8_t ucharcomdata;

// GPIO初始化和控制函数
static void InputInital()
{
    gpio_pad_select_gpio(DHT11_PIN_NUM);
    gpio_set_direction(DHT11_PIN_NUM, GPIO_MODE_INPUT);
}

static void OutputHigh(void)//输出1
{
    gpio_pad_select_gpio(DHT11_PIN_NUM);
    gpio_set_direction(DHT11_PIN_NUM, GPIO_MODE_OUTPUT);
    gpio_set_level(DHT11_PIN_NUM, 1);
}

static void OutputLow(void)//输出0
{
    gpio_pad_select_gpio(DHT11_PIN_NUM);
    gpio_set_direction(DHT11_PIN_NUM, GPIO_MODE_OUTPUT);
    gpio_set_level(DHT11_PIN_NUM, 0);
}

// 读取GPIO状态函数
static uint8_t getData()//读取状态
{
    int temp = gpio_get_level(DHT11_PIN_NUM);
	return temp;
}

// 数据通信函数
// 函数用于从DHT11传感器读取一个字节的数据。通过检测电平变化来判断每一位是0还是1。
static void COM(void)
{
    uint8_t i;
    for(i=0;i<8;i++)
    {
        ucharFLAG = 2;
        while((getData()==0) && ucharFLAG++) ets_delay_us(10);
        ets_delay_us(35);
        uchartemp = 0;

        if(getData() == 1) uchartemp = 1;
        ucharFLAG = 2;

        while((getData() == 1) && ucharFLAG++) ets_delay_us(10);
        if(ucharFLAG == 1) break;
        ucharcomdata = ucharcomdata << 1;
        ucharcomdata |= uchartemp;
    }
}

// 延时函数
static void Delay_ms(uint16_t xms)
{
    int i = 0;
    for(i=0;i < xms; i++)
    {
        ets_delay_us(1000);
    }
}

void DHT11(void)
{
    OutputLow();
    Delay_ms(20);
    OutputHigh();

    InputInital();
    ets_delay_us(30);
    if(!getData())
    {
        ucharFLAG = 2;
        while((!getData()) && ucharFLAG++) ets_delay_us(10);
        ucharFLAG = 2;
        while((getData()) && ucharFLAG++) ets_delay_us(10);
        COM();
        ucharRH_data_H_temp = ucharcomdata;
        COM();
        ucharRH_data_L_temp = ucharcomdata;
        COM();
        ucharT_data_H_temp = ucharcomdata;
        COM();
        ucharT_data_L_temp = ucharcomdata;
        COM();
        ucharcheckdata_temp = ucharcomdata;
        OutputHigh();

        uchartemp = (ucharT_data_H_temp + ucharT_data_L_temp + ucharRH_data_H_temp + ucharRH_data_L_temp);
        if(uchartemp == ucharcheckdata_temp)
        {
            ucharRH_data_H = ucharRH_data_H_temp; 
            ucharRH_data_L = ucharRH_data_L_temp;
            ucharT_data_H = ucharT_data_H_temp;
            ucharT_data_L = ucharT_data_L_temp;

            Humidity = ucharRH_data_H;
            Temperature = ucharT_data_H;
        }
        else
        {
            Humidity = 100;
            Temperature = 100;
        }
    }
    else
    {
        Humidity = 0;
        Temperature = 0;
    }
}
