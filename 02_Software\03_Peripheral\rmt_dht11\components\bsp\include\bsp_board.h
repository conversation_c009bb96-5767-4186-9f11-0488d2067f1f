/*
 * SPDX-FileCopyrightText: 2024-2025 Mcu_Electronics_Studio (Hubei) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#pragma once

#include <stdbool.h>
#include "esp_err.h"
#include "driver/i2s_std.h"

#include "esp-bsp.h"
#include "iot_button.h"

/**
 * @file bsp_board.h
 * @brief Board Support Package (BSP) for initializing and controlling various hardware components.
 */

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the board.
 * 
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_board_init();

/**
 * @brief Set the playback frequency and bit configuration for the codec.
 * 
 * @param rate The sample rate in Hz.
 * @param bits_cfg The bit configuration.
 * @param ch The I2S slot mode.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_codec_set_play_fs(uint32_t rate, uint32_t bits_cfg, i2s_slot_mode_t ch);

/**
 * @brief Set the recording frequency and bit configuration for the codec.
 * 
 * @param rate The sample rate in Hz.
 * @param bits_cfg The bit configuration.
 * @param ch The I2S slot mode.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_codec_set_record_fs(uint32_t rate, uint32_t bits_cfg, i2s_slot_mode_t ch);

/**
 * @brief Set the codec volume.
 * 
 * @param volume The desired volume level.
 * @param volume_set Pointer to store the set volume level.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_codec_volume_set(int volume, int *volume_set);

/**
 * @brief Get the current codec volume.
 * 
 * @param volume_set Pointer to store the current volume level.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_codec_volume_get(int *volume_set);

/**
 * @brief Mute or unmute the codec.
 * 
 * @param enable True to mute, false to unmute.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_codec_mute_set(bool enable);

/**
 * @brief Stop the codec device.
 * 
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_codec_dev_stop(void);

/**
 * @brief Resume the codec device.
 * 
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_codec_dev_resume(void);

/**
 * @brief Initialize the codec.
 * 
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_codec_init(void);

/**
 * @brief Write audio data to the I2S interface.
 * 
 * @param audio_buffer Pointer to the audio data buffer.
 * @param len Length of the audio data buffer.
 * @param bytes_written Pointer to store the number of bytes written.
 * @param timeout_ms Timeout in milliseconds.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_i2s_write(void *audio_buffer, size_t len, size_t *bytes_written, uint32_t timeout_ms);

/**
 * @brief Read audio data from the I2S interface.
 * 
 * @param audio_buffer Pointer to the audio data buffer.
 * @param len Length of the audio data buffer.
 * @param bytes_read Pointer to store the number of bytes read.
 * @param timeout_ms Timeout in milliseconds.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_i2s_read(void *audio_buffer, size_t len, size_t *bytes_read, uint32_t timeout_ms);

/**
 * @brief Initialize the buttons.
 * 
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_btn_init(void);

/**
 * @brief Control the power amplifier for the audio codec.
 * 
 * @param enable True to enable, false to disable.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_audio_codec_pa_ctl(bool enable);

/**
 * @brief Register a callback for a button event.
 * 
 * @param btn The button identifier.
 * @param event The button event.
 * @param callback The callback function.
 * @param user_data User data to pass to the callback function.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_btn_register_callback(bsp_button_t btn, button_event_t event, button_cb_t callback, void *user_data);

/**
 * @brief Remove all callbacks for a button.
 * 
 * @param btn The button identifier.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_btn_rm_all_callback(bsp_button_t btn);

/**
 * @brief Remove a specific event callback for a button.
 * 
 * @param btn The button identifier.
 * @param event The event identifier.
 * @return esp_err_t ESP_OK on success, or an error code on failure.
 */
esp_err_t bsp_btn_rm_event_callback(bsp_button_t btn, size_t event);

#ifdef __cplusplus
}
#endif