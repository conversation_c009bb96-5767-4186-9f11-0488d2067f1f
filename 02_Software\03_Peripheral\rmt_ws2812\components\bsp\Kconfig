menu "Board Support Package"

    config BSP_ERROR_CHECK
        bool "Enable error check in BSP"
        default y
        help
            <PERSON><PERSON><PERSON> check assert the application before returning the error code.

    menu "SPIFFS - Virtual File System"
        config BSP_SPIFFS_FORMAT_ON_MOUNT_FAIL
            bool "Format SPIFFS if mounting fails"
            default n
            help
                Format SPIFFS if it fails to mount the filesystem.

        config BSP_SPIFFS_MOUNT_POINT
            string "SPIFFS mount point"
            default "/spiffs"
            help
                Mount point of SPIFFS in the Virtual File System.

        config BSP_SPIFFS_PARTITION_LABEL
            string "Partition label of SPIFFS"
            default "storage"
            help
                Partition label which stores SPIFFS.

        config BSP_SPIFFS_MAX_FILES
            int "Max files supported for SPIFFS VFS"
            default 5
            help
                Supported max files for SPIFFS in the Virtual File System.
    endmenu

endmenu
