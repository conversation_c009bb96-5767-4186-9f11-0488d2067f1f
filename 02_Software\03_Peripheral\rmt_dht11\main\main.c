/*
 * SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <freertos/queue.h>
#include "esp_log.h"
#include "esp_check.h"

#include "nvs_flash.h"
#include "bsp_board.h"

static char *TAG = "app_main";

#define LOG_MEM_INFO (1)

// 温度 湿度变量
int temp = 0,hum = 0;

void app_main(void)
{
	ESP_ERROR_CHECK(nvs_flash_init());
	vTaskDelay(100 / portTICK_PERIOD_MS);
	bsp_board_init();
	
	while (1){
		if (bsp_dht11_read(&temp, &hum) == ESP_OK){
			ESP_LOGI(TAG, "Temperature : %i.%i C     Humidity : %i%%", temp / 10, temp % 10, hum);
		}
		vTaskDelay(500 / portTICK_PERIOD_MS);
	}
}
