# RMT驱动DHT11

### 本文档介绍如何通过rmt_dht11在ESP32上驱动DHT11。

#### 1、定义DHT11引脚并创建句柄

```c
/* DHT11 */
#define BSP_DHT11_IO          (GPIO_NUM_12)

static dht11_handle_t dht11;
```

#### 2、定义初始化结构体参数

```c
/**
 * @brief dht11 configuration structure
 *
 * This configuration is used by default in bsp_dht11_init()
 */
static const dht11_config_t bsp_dht11_config = {
    .dht11_gpio_num = BSP_DHT11_IO,
    .flags.invert_in = false,
};

static const dht11_rmt_config_t bsp_dht11_rmt_config = {
    .clk_src = RMT_CLK_SRC_DEFAULT,
    .resolution_hz = 1 * 1000 * 1000,
    .flags.with_dma = false,
```

#### 3、编写初始化及读取函数

```c
esp_err_t bsp_dht11_init()
{
    ESP_LOGI(TAG, "DHT11_GPIO setting %d", bsp_dht11_config.dht11_gpio_num);

    ESP_ERROR_CHECK(dht11_new_rmt_device(&bsp_dht11_config, &bsp_dht11_rmt_config, &dht11));
    dht11_init(dht11, bsp_dht11_config.dht11_gpio_num);

    return ESP_OK;
}

esp_err_t bsp_dht11_read(int *temperature, int *humidity)
{
    esp_err_t ret = ESP_OK;

    ret = dht11_read(dht11, temperature, humidity);

    return ret;
}
```

#### 4、初始化并读取

```c
// 温度 湿度变量
int temp = 0,hum = 0;

void app_main(void)
{
	ESP_ERROR_CHECK(nvs_flash_init());
	vTaskDelay(100 / portTICK_PERIOD_MS);
	bsp_board_init();
	
	while (1){
		if (bsp_dht11_read(&temp, &hum) == ESP_OK){
			ESP_LOGI(TAG, "Temperature : %i.%i C     Humidity : %i%%", temp / 10, temp % 10, hum);
		}
		vTaskDelay(500 / portTICK_PERIOD_MS);
	}
}
```
