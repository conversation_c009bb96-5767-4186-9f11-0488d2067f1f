/**
 * @FilePath: dht11_interface.h
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-13 12:25:17
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-10-13 17:15:56
 * @Copyright: 2024 0668STO CO.,LTD. All Rights Reserved.
*/
#pragma once

#include <stdint.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct dht11_t dht11_t; /*!< Type of DHT11 sensor */

/**
 * @brief DHT11 sensor interface definition
 */
struct dht11_t {
    /**
     * @brief Initialize the DHT11 sensor
     *
     * @param sensor: DHT11 sensor
     * @param gpio_num: GPIO number connected to DHT11
     *
     * @return
     *      - ESP_OK: Initialize successfully
     *      - ESP_ERR_INVALID_ARG: Initialize failed because of invalid parameters
     */
    esp_err_t (*init)(struct dht11_t *sensor, int gpio_num);

    /**
     * @brief Read temperature and humidity from DHT11 sensor
     *
     * @param sensor: DHT11 sensor
     * @param temperature: pointer to store temperature value
     * @param humidity: pointer to store humidity value
     *
     * @return
     *      - ESP_OK: Read successfully
     *      - ESP_ERR_INVALID_ARG: Read failed because of invalid parameters
     *      - ESP_FAIL: Read failed because other error occurred
     */
    esp_err_t (*read)(struct dht11_t *sensor, int *temperature, int *humidity);

    /**
     * @brief Deinitialize the DHT11 sensor
     *
     * @param sensor: DHT11 sensor
     *
     * @return
     *      - ESP_OK: Deinitialize successfully
     *      - ESP_FAIL: Deinitialize failed because other error occurred
     */
    esp_err_t (*deinit)(struct dht11_t *sensor);
};

#ifdef __cplusplus
}
#endif