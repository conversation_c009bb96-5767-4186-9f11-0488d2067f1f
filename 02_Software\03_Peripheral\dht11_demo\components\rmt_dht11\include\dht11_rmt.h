/**
 * @FilePath: dht11_rmt.h
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-13 12:25:17
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-10-13 17:15:56
 * @Copyright: 2024 0668STO CO.,LTD. All Rights Reserved.
*/
#pragma once

#include <stdint.h>
#include "esp_err.h"
#include "esp_idf_version.h"
#include "dht11_types.h"

#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
#include "driver/rmt_types.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief DHT11 RMT specific configuration
 */
typedef struct {
#if ESP_IDF_VERSION < ESP_IDF_VERSION_VAL(5, 0, 0)
    uint8_t rmt_channel;        /*!< Specify the channel number, the legacy RMT driver doesn't support channel allocator */
#else // new driver supports specify the clock source and clock resolution
    rmt_clock_source_t clk_src; /*!< RMT clock source */
    uint32_t resolution_hz;     /*!< RMT tick resolution, if set to zero, a default resolution (10MHz) will be applied */
#endif
    size_t mem_block_symbols;   /*!< How many RMT symbols can one RMT channel hold at one time. Set to 0 will fallback to use the default size. */
    struct {
        uint32_t with_dma: 1;   /*!< Use DMA to receive data */
    } flags;                    /*!< Extra driver flags */
} dht11_rmt_config_t;

/**
 * @brief Create DHT11 sensor based on RMT RX channel
 *
 * @param dht11_config DHT11 sensor configuration
 * @param rmt_config RMT specific configuration
 * @param ret_sensor Returned DHT11 sensor handle
 * @return
 *      - ESP_OK: create DHT11 sensor handle successfully
 *      - ESP_ERR_INVALID_ARG: create DHT11 sensor handle failed because of invalid argument
 *      - ESP_ERR_NO_MEM: create DHT11 sensor handle failed because of out of memory
 *      - ESP_FAIL: create DHT11 sensor handle failed because some other error
 */
esp_err_t dht11_new_rmt_device(const dht11_config_t *dht11_config, const dht11_rmt_config_t *rmt_config, dht11_handle_t *ret_sensor);

#ifdef __cplusplus
}
#endif