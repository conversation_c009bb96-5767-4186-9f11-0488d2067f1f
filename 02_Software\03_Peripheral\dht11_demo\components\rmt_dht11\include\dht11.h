/**
 * @FilePath: dht11.h
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-13 12:29:37
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-10-13 13:48:44
 * @Copyright: 2024 0668STO CO.,LTD. All Rights Reserved.
*/
#pragma once

#include <stdint.h>
#include "esp_err.h"
#include "dht11_rmt.h"
#include "esp_idf_version.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the DHT11 sensor.
 *
 * @param dht11 Handle to the DHT11 sensor.
 * @param gpio_num GPIO number where the DHT11 sensor is connected.
 * @return 
 *     - ESP_OK: Success
 *     - ESP_ERR_INVALID_ARG: Invalid argument
 *     - ESP_FAIL: Initialization failed
 */
esp_err_t dht11_init(dht11_handle_t dht11, int gpio_num);

/**
 * @brief Read temperature and humidity from the DHT11 sensor.
 *
 * @param dht11 Handle to the DHT11 sensor.
 * @param temperature Pointer to store the read temperature value.
 * @param humidity Pointer to store the read humidity value.
 * @return 
 *     - ESP_OK: Success
 *     - ESP_ERR_INVALID_ARG: Invalid argument
 *     - ESP_FAIL: Read operation failed
 */
esp_err_t dht11_read(dht11_handle_t dht11, int *temperature, int *humidity);

/**
 * @brief Deinitialize the DHT11 sensor.
 *
 * @param dht11 Handle to the DHT11 sensor.
 * @return 
 *     - ESP_OK: Success
 *     - ESP_ERR_INVALID_ARG: Invalid argument
 *     - ESP_FAIL: Deinitialization failed
 */
esp_err_t dht11_deinit(dht11_handle_t dht11);

#ifdef __cplusplus
}
#endif
