# ESP GPIO 操作指南

### 本文档介绍了如何在ESP32上操作GPIO，包括选择GPIO、设置方向以及设置电平。

#### 基本操作

以下是一些基本的GPIO操作示例：

```c
gpio_pad_select_gpio(GPIO_NUM_22);                // 选择一个GPIO
gpio_set_direction(GPIO_NUM_22, GPIO_MODE_OUTPUT);// 把这个GPIO作为输出
gpio_set_level(GPIO_NUM_22, 0);                   // 把这个GPIO输出低电平
```

#### 定义宏

为了简化GPIO的配置，可以使用宏定义：

```c
#define GPIO_OUTPUT_IO_0    22
#define GPIO_OUTPUT_PIN_SEL  (1ULL<<GPIO_OUTPUT_IO_0)  // 配置GPIO_OUT位寄存器
```

#### 初始化GPIO

以下是一个初始化GPIO的函数示例：

```c
void gpio_init(void)
{
    gpio_config_t io_conf;  // 定义一个gpio_config类型的结构体，下面的都算对其进行的配置
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;  // 禁止中断  
    io_conf.mode = GPIO_MODE_OUTPUT;            // 选择输出模式
    io_conf.pin_bit_mask = GPIO_OUTPUT_PIN_SEL; // 配置GPIO_OUT寄存器
    io_conf.pull_down_en = 0;                   // 禁止下拉
    io_conf.pull_up_en = 0;                     // 禁止上拉

    gpio_config(&io_conf);                      // 最后配置使能
}
```
#### 设置GPIO电平

以下是设置GPIO电平的示例：

```c
gpio_set_level(GPIO_OUTPUT_IO_0, 0);            // 把这个GPIO输出低电平
gpio_set_level(GPIO_OUTPUT_IO_0, 1);            // 把这个GPIO输出高电平
```

以上分别是两种操作GPIO的方法。



RMT:[红外遥控 (RMT) - ESP32 - — ESP-IDF 编程指南 latest 文档](https://docs.espressif.com/projects/esp-idf/zh_CN/latest/esp32/api-reference/peripherals/rmt.html)

DHT11通信原理讲解：[DHT11详细介绍（内含51和STM32代码）-CSDN博客](https://blog.csdn.net/m0_55849362/article/details/126426768)
DHT11(ESP32)通信原理讲解：[ESP32驱动DHT11检测温湿度(ESP IDF环境)_dht11esp32-CSDN博客](https://blog.csdn.net/Changerking/article/details/122366815)
