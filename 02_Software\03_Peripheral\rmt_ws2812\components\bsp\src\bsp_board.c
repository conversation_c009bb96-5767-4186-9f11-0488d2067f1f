/*
 * SPDX-FileCopyrightText: 2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/task.h"
#include "esp_task_wdt.h"
#include "esp_check.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_spiffs.h"
#include "esp_vfs.h"

#include "bsp_board.h"
#include "esp-bsp.h"

#include "driver/gpio.h"
#include "rom/gpio.h"

static const char *TAG = "bsp_board";

#define CODEC_DEFAULT_SAMPLE_RATE (16000)
#define CODEC_DEFAULT_BIT_WIDTH (16)
#define CODEC_DEFAULT_CHANNEL (1)

static esp_codec_dev_handle_t play_dev_handle;
static esp_codec_dev_handle_t record_dev_handle;
static button_handle_t *g_btn_handle = NULL;

esp_err_t bsp_btn_init(void)
{
    ESP_ERROR_CHECK((NULL != g_btn_handle));

    int btn_num = 0;
    g_btn_handle = calloc(sizeof(button_handle_t), BSP_BUTTON_NUM);
    assert((g_btn_handle) && "memory is insufficient for button");
    return bsp_iot_button_create(g_btn_handle, &btn_num, BSP_BUTTON_NUM);
}

esp_err_t bsp_btn_register_callback(bsp_button_t btn, button_event_t event, button_cb_t callback, void *user_data)
{
    assert((g_btn_handle) && "button not initialized");
    assert((btn < BSP_BUTTON_NUM) && "button id incorrect");

    if (NULL == callback)
    {
        return iot_button_unregister_cb(g_btn_handle[btn], event);
    }
    return iot_button_register_cb(g_btn_handle[btn], event, callback, user_data);
}

esp_err_t bsp_btn_rm_all_callback(bsp_button_t btn)
{
    assert((g_btn_handle) && "button not initialized");
    assert((btn < BSP_BUTTON_NUM) && "button id incorrect");

    for (size_t event = 0; event < BUTTON_EVENT_MAX; event++)
    {
        iot_button_unregister_cb(g_btn_handle[btn], event);
    }
    return ESP_OK;
}

esp_err_t bsp_btn_rm_event_callback(bsp_button_t btn, size_t event)
{
    assert((g_btn_handle) && "button not initialized");
    assert((btn < BSP_BUTTON_NUM) && "button id incorrect");

    iot_button_unregister_cb(g_btn_handle[btn], event);
    return ESP_OK;
}

esp_err_t bsp_codec_set_play_fs(uint32_t rate, uint32_t bits_cfg, i2s_slot_mode_t ch)
{
    esp_err_t ret = ESP_OK;

    esp_codec_dev_sample_info_t play_fs = {
        .sample_rate = rate,
        .channel = ch,
        .bits_per_sample = bits_cfg,
    };

    if (play_dev_handle)
    {
        ret = esp_codec_dev_close(play_dev_handle);
    }

    if (play_dev_handle)
    {
        ret |= esp_codec_dev_open(play_dev_handle, &play_fs);
    }

    return ret;
}

esp_err_t bsp_codec_set_record_fs(uint32_t rate, uint32_t bits_cfg, i2s_slot_mode_t ch)
{
    esp_err_t ret = ESP_OK;

    esp_codec_dev_sample_info_t rec_fs = {
        .sample_rate = rate,
        .channel = ch,
        .bits_per_sample = bits_cfg,
    };

    if (record_dev_handle)
    {
        ret = esp_codec_dev_close(record_dev_handle);
    }

    if (record_dev_handle)
    {
        ret |= esp_codec_dev_open(record_dev_handle, &rec_fs);
    }

    return ret;
}

esp_err_t bsp_codec_volume_set(int volume, int *volume_set)
{
    esp_err_t ret = ESP_OK;
    float v = volume;
    ret = esp_codec_dev_set_out_vol(play_dev_handle, (int)v);
    return ret;
}

esp_err_t bsp_codec_volume_get(int *volume_set)
{
    esp_err_t ret = ESP_OK;
    int v;
    ret = esp_codec_dev_get_out_vol(play_dev_handle, (int *)&v);
    printf("%d\n", v);
    return ret;
}

esp_err_t bsp_codec_gain_set(esp_codec_dev_handle_t handle, float db)
{
    esp_err_t ret = ESP_OK;
    float v = db;
    ret = esp_codec_dev_set_in_gain(record_dev_handle, v);
    return ret;
}

esp_err_t bsp_codec_mute_set(bool enable)
{
    esp_err_t ret = ESP_OK;
    ret = esp_codec_dev_set_out_mute(play_dev_handle, enable);
    return ret;
}

esp_err_t bsp_codec_dev_stop(void)
{
    esp_err_t ret = ESP_OK;

    if (play_dev_handle)
    {
        ret = esp_codec_dev_close(play_dev_handle);
    }

    if (record_dev_handle)
    {
        ret = esp_codec_dev_close(record_dev_handle);
    }
    return ret;
}

esp_err_t bsp_codec_dev_resume(void)
{
    return bsp_codec_set_play_fs(CODEC_DEFAULT_SAMPLE_RATE, CODEC_DEFAULT_BIT_WIDTH, CODEC_DEFAULT_CHANNEL);
}

esp_err_t bsp_i2s_write(void *audio_buffer, size_t len, size_t *bytes_written, uint32_t timeout_ms)
{
    esp_err_t ret = ESP_OK;
    ret = esp_codec_dev_write(play_dev_handle, audio_buffer, len);
    *bytes_written = len;
    return ret;
}

esp_err_t bsp_i2s_read(void *audio_buffer, size_t len, size_t *bytes_read, uint32_t timeout_ms)
{
    esp_err_t ret = ESP_OK;
    ret = esp_codec_dev_read(record_dev_handle, audio_buffer, len);
    *bytes_read = len;
    return ret;
}

esp_err_t bsp_codec_init(void)
{
    record_dev_handle = bsp_audio_codec_microphone_init();
    assert((record_dev_handle) && "record_dev_handle not initialized");

    play_dev_handle = bsp_audio_codec_speaker_init();
    assert((play_dev_handle) && "play_dev_handle not initialized");

    bsp_codec_set_play_fs(CODEC_DEFAULT_SAMPLE_RATE, CODEC_DEFAULT_BIT_WIDTH, CODEC_DEFAULT_CHANNEL);
    bsp_codec_set_record_fs(CODEC_DEFAULT_SAMPLE_RATE, CODEC_DEFAULT_BIT_WIDTH, CODEC_DEFAULT_CHANNEL);

    return ESP_OK;
}

esp_err_t bsp_audio_codec_pa_ctl(bool enable)
{
    esp_err_t ret = ESP_OK;
    gpio_pad_select_gpio(BSP_SPEAKER_PA_IO);
    ret |= gpio_set_direction(BSP_SPEAKER_PA_IO, GPIO_MODE_OUTPUT);
    ret |= gpio_set_level(BSP_SPEAKER_PA_IO, enable);

    return ret;
}

esp_err_t bsp_board_init()
{
    esp_err_t ret = ESP_OK;

    ESP_ERROR_CHECK(bsp_codec_init());
    ESP_ERROR_CHECK(bsp_audio_codec_pa_ctl(true));

    ESP_ERROR_CHECK(bsp_btn_init());
    ESP_ERROR_CHECK(bsp_led_init());

    return ret;
}
