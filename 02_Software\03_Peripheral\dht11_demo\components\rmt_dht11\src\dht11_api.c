/**
 * @FilePath: dht11_api.c
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-13 12:25:17
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-10-13 17:15:56
 * @Copyright: 2024 0668STO CO.,LTD. All Rights Reserved.
*/

#include "esp_log.h"
#include "esp_check.h"
#include "dht11.h"
#include "dht11_interface.h"

static const char *TAG = "dht11_api";

esp_err_t dht11_init(dht11_handle_t dht11, int gpio_num)
{
    ESP_RETURN_ON_FALSE(dht11, ESP_ERR_INVALID_ARG, TAG, "invalid argument");
    return dht11->init(dht11, gpio_num);
}

esp_err_t dht11_read(dht11_handle_t dht11, int *temperature, int *humidity)
{
    ESP_RETURN_ON_FALSE(dht11, ESP_ERR_INVALID_ARG, TAG, "invalid argument");
    return dht11->read(dht11, temperature, humidity);
}

esp_err_t dht11_deinit(dht11_handle_t dht11)
{
    ESP_RETURN_ON_FALSE(dht11, ESP_ERR_INVALID_ARG, TAG, "invalid argument");
    return dht11->deinit(dht11);
}