menu "Example Configuration"

    choice ENTER_LIGHT_SLEEP_MODE
        prompt "Enter light sleep mode"
        default ENTER_LIGHT_SLEEP_AUTO
        depends on PM_ENABLE
        help
            Enable light sleep mode to save power.

        config ENTER_LIGHT_SLEEP_AUTO
            bool "Auto enter Light Sleep"
        config ENTER_LIGHT_SLEEP_MODE_MANUALLY
            bool "Manually enter Light Sleep"
    endchoice

    choice EXAMPLE_MAX_CPU_FREQ
        prompt "Maximum CPU frequency"
        default EXAMPLE_MAX_CPU_FREQ_80 if !IDF_TARGET_ESP32H2
        default EXAMPLE_MAX_CPU_FREQ_96 if IDF_TARGET_ESP32H2
        depends on PM_ENABLE
        help
            Maximum CPU frequency to use for dynamic frequency scaling.

        config EXAMPLE_MAX_CPU_FREQ_80
            bool "80 MHz"
        config EXAMPLE_MAX_CPU_FREQ_96
            bool "96 MHz"
            depends on IDF_TARGET_ESP32H2
        config EXAMPLE_MAX_CPU_FREQ_120
            bool "120 MHz"
            depends on IDF_TARGET_ESP32C2
        config EXAMPLE_MAX_CPU_FREQ_160
            bool "160 MHz"
            depends on !IDF_TARGET_ESP32C2
        config EXAMPLE_MAX_CPU_FREQ_240
            bool "240 MHz"
            depends on IDF_TARGET_ESP32 || IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
    endchoice

    config EXAMPLE_MAX_CPU_FREQ_MHZ
        int
        default 80 if EXAMPLE_MAX_CPU_FREQ_80
        default 96 if EXAMPLE_MAX_CPU_FREQ_96
        default 120 if EXAMPLE_MAX_CPU_FREQ_120
        default 160 if EXAMPLE_MAX_CPU_FREQ_160
        default 240 if EXAMPLE_MAX_CPU_FREQ_240

    choice EXAMPLE_MIN_CPU_FREQ
        prompt "Minimum CPU frequency"
        default EXAMPLE_MIN_CPU_FREQ_10M if !IDF_TARGET_ESP32H2
        default EXAMPLE_MIN_CPU_FREQ_32M if IDF_TARGET_ESP32H2
        depends on PM_ENABLE
        help
            Minimum CPU frequency to use for dynamic frequency scaling.
            Should be set to XTAL frequency or XTAL frequency divided by integer.

        config EXAMPLE_MIN_CPU_FREQ_40M
            bool "40 MHz (use with 40MHz XTAL)"
            depends on XTAL_FREQ_40 || XTAL_FREQ_AUTO || ESP32_XTAL_FREQ_40 || ESP32_XTAL_FREQ_AUTO || !IDF_TARGET_ESP32
        config EXAMPLE_MIN_CPU_FREQ_20M
            bool "20 MHz (use with 40MHz XTAL)"
            depends on XTAL_FREQ_40 || XTAL_FREQ_AUTO || ESP32_XTAL_FREQ_40 || ESP32_XTAL_FREQ_AUTO || !IDF_TARGET_ESP32
        config EXAMPLE_MIN_CPU_FREQ_10M
            bool "10 MHz (use with 40MHz XTAL)"
            depends on XTAL_FREQ_40 || XTAL_FREQ_AUTO || ESP32_XTAL_FREQ_40 || ESP32_XTAL_FREQ_AUTO || !IDF_TARGET_ESP32
        config EXAMPLE_MIN_CPU_FREQ_26M
            bool "26 MHz (use with 26MHz XTAL)"
            depends on XTAL_FREQ_26 || XTAL_FREQ_AUTO || ESP32_XTAL_FREQ_26 || ESP32_XTAL_FREQ_AUTO
        config EXAMPLE_MIN_CPU_FREQ_13M
            bool "13 MHz (use with 26MHz XTAL)"
            depends on XTAL_FREQ_26 || XTAL_FREQ_AUTO || ESP32_XTAL_FREQ_26 || ESP32_XTAL_FREQ_AUTO
        config EXAMPLE_MIN_CPU_FREQ_32M
            bool "32 MHz (use with 32MHz XTAL)"
            depends on IDF_TARGET_ESP32H2
            depends on XTAL_FREQ_32 || XTAL_FREQ_AUTO
    endchoice

    config EXAMPLE_MIN_CPU_FREQ_MHZ
        int
        default 40 if EXAMPLE_MIN_CPU_FREQ_40M
        default 20 if EXAMPLE_MIN_CPU_FREQ_20M
        default 10 if EXAMPLE_MIN_CPU_FREQ_10M
        default 26 if EXAMPLE_MIN_CPU_FREQ_26M
        default 13 if EXAMPLE_MIN_CPU_FREQ_13M
        default 32 if EXAMPLE_MIN_CPU_FREQ_32M

endmenu
