include($ENV{IDF_PATH}/tools/cmake/version.cmake)

set(srcs "src/dht11_api.c")
set(public_requires)

list(APPEND srcs "src/dht11_rmt_dev.c")

# Starting from esp-idf v5.3, the RMT driver is moved to a separate component
if("${IDF_VERSION_MAJOR}.${IDF_VERSION_MINOR}" VERSION_GREATER_EQUAL "5.3")
    list(APPEND public_requires "esp_driver_rmt")
else()
    list(APPEND public_requires "driver")
endif()

idf_component_register(SRCS ${srcs}
                       INCLUDE_DIRS "include" "interface"
                       REQUIRES ${public_requires})